import usePoiList, { PoiItem } from '@src/pages/knowledge/chat/common/service/poiList';
import noContentImg from '@src/assets/images/no-content.png';
import { Avatar, Empty, Input, List, Checkbox, Space, Spin } from 'antd';
import VirtualList from 'rc-virtual-list';
import { useEffect, useState, useMemo } from 'react';
import { usePrevious } from 'ahooks';

interface PoiSelectorProps {
    onChange?: (selectedPois: PoiItem[]) => void;
    defaultPoiList?: PoiItem[];
    defaultSelected?: boolean;
}

const PoiSelector = ({ onChange, defaultPoiList = [], defaultSelected = false }: PoiSelectorProps) => {
    const { onScroll, data, setSearchValue, loading } = usePoiList();
    const [selectedPois, setSelectedPois] = useState<PoiItem[]>([]);
    const previousData = usePrevious(data);
    // 合并默认POI列表和搜索结果，默认POI置顶
    const mergedPoiList = useMemo(() => {
        const searchList = data?.list || [];
        if (!defaultPoiList.length) {
            return searchList;
        }

        // 过滤掉搜索结果中已存在于默认列表的POI，避免重复
        const filteredSearchList = searchList.filter(
            searchPoi => !defaultPoiList.some(defaultPoi => defaultPoi.id === searchPoi.id)
        );

        return [...defaultPoiList, ...filteredSearchList];
    }, [data?.list, defaultPoiList]);

    // 初始化默认选中项
    useEffect(() => {
        if (defaultSelected && defaultPoiList.length > 0) {
            setSelectedPois(defaultPoiList);
        }
    }, [defaultPoiList, defaultSelected]);

    // 处理POI选择/取消选择
    const handlePoiToggle = (poi: PoiItem) => {
        setSelectedPois(prev => {
            const isSelected = prev.some(p => p.id === poi.id);
            if (isSelected) {
                return prev.filter(p => p.id !== poi.id);
            } else {
                return [...prev, poi];
            }
        });
    };

    // 通知父组件选中项变化
    useEffect(() => {
        // 搜索中不触发onChange
        if (loading) {
            return;
        }
        onChange?.(selectedPois);
    }, [selectedPois, loading, onChange]);
    return (
        <Space direction={'vertical'} style={{ width: '100%' }}>
            <Input.Search
                placeholder={'请输入商家名称'}
                onChange={e => setSearchValue(e.target.value)}
                style={{ width: '100%' }}
            />
            <Spin spinning={loading}>
                <List style={{ width: '100%' }} locale={{ emptyText: '暂无数据' }}>
                    {!mergedPoiList?.length ? (
                        <Empty
                            description={'仅支持查询您名下的商家，请确保输入信息无误'}
                            image={noContentImg}
                            style={{ margin: '30px auto' }}
                        />
                    ) : (
                        <VirtualList
                            data={mergedPoiList}
                            height={400}
                            itemHeight={47}
                            style={{ scrollbarWidth: 'none' }}
                            itemKey={item => item?.id}
                            onScroll={onScroll}
                        >
                            {item => {
                                const isSelected = selectedPois.some(p => p.id === item.id);
                                const isDefault = defaultPoiList.some(p => p.id === item.id);

                                return (
                                    <Space
                                        onClick={() => handlePoiToggle(item)}
                                        className={'pointer'}
                                        style={{
                                            backgroundColor: isDefault ? '#f6ffed' : 'transparent',
                                            padding: '4px 8px',
                                            borderRadius: '4px',
                                            width: '100%'
                                        }}
                                    >
                                        <Checkbox checked={isSelected} />
                                        <Avatar src={item.url} />
                                        <div style={{ flex: 1 }}>
                                            <div className={'f_14 c_222'}>
                                                {item.name}
                                                {isDefault && <span style={{ color: '#52c41a', marginLeft: '8px' }}>(默认)</span>}
                                            </div>
                                            <div className={'f_12 c_666'}>ID: {item.id}</div>
                                        </div>
                                    </Space>
                                );
                            }}
                        </VirtualList>
                    )}
                </List>
            </Spin>
        </Space>
    );
};
export default PoiSelector;
